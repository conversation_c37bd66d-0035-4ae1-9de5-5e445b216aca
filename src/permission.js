import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false })

// 模拟用户信息，去掉登录校验
const mockUserInfo = {
  roles: ['admin'],
  permissions: ['*:*:*'],
  name: 'admin',
  avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
}

router.beforeEach((to, from, next) => {
  NProgress.start()

  // 设置页面标题
  to.meta.title && store.dispatch('settings/setTitle', to.meta.title)

  // 模拟已登录状态，设置用户信息
  if (store.getters.roles.length === 0) {
    // 设置模拟的用户信息
    store.commit('SET_ROLES', mockUserInfo.roles)
    store.commit('SET_PERMISSIONS', mockUserInfo.permissions)
    store.commit('SET_NAME', mockUserInfo.name)
    store.commit('SET_AVATAR', mockUserInfo.avatar)

    // 生成路由
    store.dispatch('GenerateRoutes').then(accessRoutes => {
      // 根据roles权限生成可访问的路由表
      router.addRoutes(accessRoutes) // 动态添加可访问路由表
      next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
    }).catch(err => {
      console.error('路由生成失败:', err)
      next()
    })
  } else {
    next()
  }
})

router.afterEach(() => {
  NProgress.done()
})
